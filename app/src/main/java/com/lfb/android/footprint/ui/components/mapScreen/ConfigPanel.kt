package com.lfb.android.footprint.ui.components.mapScreen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.res.painterResource
import com.lfb.android.footprint.R
import androidx.compose.ui.graphics.painter.Painter
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.ui.theme.MapThemeManager

/**
 * 颜色转换工具函数
 */
fun colorToHexString(color: Color): String {
    // 使用 toArgb() 方法获取正确的 ARGB 值
    val argb = color.toArgb().toUInt()
    return "0x${argb.toString(16).uppercase().padStart(8, '0')}"
}

fun hexStringToColor(hexString: String): Color {
    return try {
        val cleanHex = hexString.removePrefix("0x").removePrefix("#")
        // 使用 toLong() 然后转换为 Int，再通过 Color 构造函数创建
        val argb = cleanHex.toLong(16).toInt()
        Color(argb)
    } catch (e: Exception) {
        println("ConfigPanel: Error parsing color $hexString: ${e.message}")
        Color(0xFFFF0000) // 默认红色
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConfigPanel(
    modifier: Modifier = Modifier,
    onClose: () -> Unit,
    onMapDisplayTypeChanged: (Int) -> Unit = {},
    onMapShowAddressNameChanged: (Boolean) -> Unit = {},
    onMapDrawLineAlphaChanged: (Double) -> Unit = {},
    onMapDrawSpotAlphaChanged: (Double) -> Unit = {},
    onMapDrawLineWidthChanged: (Int) -> Unit = {},
    onMapDrawLineColorChanged: (String) -> Unit = {},
    onMapDrawLineDistanceFilterChanged: (Int) -> Unit = {}
) {

    ModalBottomSheet(
        onDismissRequest = onClose,
        modifier = modifier
            .fillMaxWidth()
            .height(400.dp) // Increased height to match design
                ,
        shape = RoundedCornerShape(topStart = 22.dp, topEnd = 22.dp),
        containerColor = Color(0xFF000000), // Pure black background to match design
        dragHandle = null // 添加这一行来移除拖拽手柄
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
//                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp, vertical = 8.dp)
        ) {
            // Map Display Section - 使用 AppPrefs 中的配置
            var showPhotoData by remember { mutableStateOf(false) }
            var showAnnotationData by remember { mutableStateOf(false) }
            var showMapNames by remember { mutableStateOf(AppPrefs.sharedInstance.mapShowAdressName) }

            Spacer(modifier = Modifier.height(24.dp))

            ConfigItem(
                label = "| 地图显示",
                content = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
//                        ConfigToggleButton(
//                            text = "显示照片数据",
//                            isChecked = showPhotoData,
//                            onCheckedChange = { showPhotoData = it },
//                            modifier = Modifier.weight(1f)
//                        )
//                        ConfigToggleButton(
//                            text = "显示标注数据",
//                            isChecked = showAnnotationData,
//                            onCheckedChange = { showAnnotationData = it },
//                            modifier = Modifier.weight(1f)
//                        )
                        ConfigToggleButton(
                            text = "地图显示地名",
                            isChecked = showMapNames,
                            onCheckedChange = {
                                showMapNames = it
                                AppPrefs.sharedInstance.mapShowAdressName = it
                                onMapShowAddressNameChanged(it)
                            },
                            modifier = Modifier.width(94.dp)
                        )
                    }
                }
            )
            Spacer(modifier = Modifier.height(12.dp))

            // Map Style Section - 使用 AppPrefs 中的配置
            val mapStyleNames = listOf("通用地图", "清新蓝", "象牙白", "薄荷绿", "樱桃粉", "卫星图", "户外地图")
            var selectedMapStyle by remember {
                mutableStateOf(mapStyleNames.getOrElse(AppPrefs.sharedInstance.mapDisplayType) { "通用地图" })
            }

            ConfigItem(
                label = "| 地图样式",
                content = {
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        item {
                            MapStyleButton(
                                text = "通用地图",
                                isSelected = selectedMapStyle == "通用地图",
                                onClick = {
                                    selectedMapStyle = "通用地图"
                                    MapThemeManager.updateMapDisplayType(0)
                                    onMapDisplayTypeChanged(0)
                                },
                                backgroundImage = painterResource(id = R.drawable.config_normalmap)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "清新蓝",
                                isSelected = selectedMapStyle == "清新蓝",
                                onClick = {
                                    selectedMapStyle = "清新蓝"
                                    MapThemeManager.updateMapDisplayType(1)
                                    onMapDisplayTypeChanged(1)
                                },
                                backgroundImage = painterResource(id = R.drawable.config_map_gray)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "象牙白",
                                isSelected = selectedMapStyle == "象牙白",
                                onClick = {
                                    selectedMapStyle = "象牙白"
                                    MapThemeManager.updateMapDisplayType(2)
                                    onMapDisplayTypeChanged(2)
                                },
                                backgroundImage = painterResource(id = R.drawable.config_map_white)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "薄荷绿",
                                isSelected = selectedMapStyle == "薄荷绿",
                                onClick = {
                                    selectedMapStyle = "薄荷绿"
                                    MapThemeManager.updateMapDisplayType(3)
                                    onMapDisplayTypeChanged(3)
                                },
                                backgroundImage = painterResource(id = R.drawable.config_map_green)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "樱桃粉",
                                isSelected = selectedMapStyle == "樱桃粉",
                                onClick = {
                                    selectedMapStyle = "樱桃粉"
                                    MapThemeManager.updateMapDisplayType(4)
                                    onMapDisplayTypeChanged(4)
                                },
                                backgroundImage = painterResource(id = R.drawable.config_map_pink)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "卫星图",
                                isSelected = selectedMapStyle == "卫星图",
                                onClick = {
                                    selectedMapStyle = "卫星图"
                                    MapThemeManager.updateMapDisplayType(5)
                                    onMapDisplayTypeChanged(5)
                                },
                                backgroundImage = painterResource(id = R.drawable.config_map_satellite)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "户外地图",
                                isSelected = selectedMapStyle == "户外地图",
                                onClick = {
                                    selectedMapStyle = "户外地图"
                                    MapThemeManager.updateMapDisplayType(6)
                                    onMapDisplayTypeChanged(6)
                                },
                                backgroundImage = painterResource(id = R.drawable.config_map_outdoors)
                            )
                        }
                    }
                }
            )
            Spacer(modifier = Modifier.height(12.dp))

            // Track Color Section - matching design colors exactly
            var selectedTrackColor by remember {
                val savedColor = AppPrefs.sharedInstance.mapDrawLineColor
                val convertedColor = hexStringToColor(savedColor)
                println("ConfigPanel: Loading saved color: $savedColor")
                println("ConfigPanel: Converted to Color: ${convertedColor.toArgb().toUInt().toString(16)}")
                mutableStateOf(convertedColor)
            }
            val trackColors = listOf(
                Color(0xFFFF0000), Color(0xFFEE5147), Color(0xFFF78356), Color(0xFFF8954A),
                Color(0xFFCBCB2D), Color(0xFFB1D441), Color(0xFF6AD070), Color(0xFF45C79D),
                Color(0xFF53AAEB), Color(0xFF007AFB), Color(0xFF638AF1), Color(0xFF877BFC),
                Color(0xFF7C2DED), Color(0xFFB684EE), Color(0xFFE286EE), Color(0xFFF789CE),
                Color(0xFFF77196), Color(0xFF5697A8), Color(0xFFDBD9DB), Color(0xFFF3F1F3),
                Color(0xFFFFFFFF), Color(0xFF222222)
            )

            ConfigItem(
                label = "| 轨迹颜色",
                content = {
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        trackColors.forEach { color ->
                            item {
                                println("selectedTrackColor = $selectedTrackColor")
                                ColorSelectionItem(
                                    color = color,
                                    isSelected = selectedTrackColor == color,
                                    onClick = {
                                        selectedTrackColor = color
                                        val colorHex = colorToHexString(color)
                                        println("ConfigPanel: Selected color ARGB: ${color.toArgb().toUInt().toString(16)}")
                                        println("ConfigPanel: Converted to hex: $colorHex")
                                        AppPrefs.sharedInstance.mapDrawLineColor = colorHex
                                        onMapDrawLineColorChanged(colorHex)
                                    },
                                    modifier = Modifier.size(32.dp)
                                )
                            }
                        }
                    }
                }
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Sliders - 使用 AppPrefs 中的配置
            var trackLineOpacity by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineAlpha.toFloat()) }
            var trackPointOpacity by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawSpotAlpha.toFloat()) }
            var trackLineWidth by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineWidth.toFloat()) }
            var trackConnectionDistance by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineDistanceFilter.toFloat() / 15000f) }

            ConfigItem(
                label = "| 轨迹线透明度",
                content = {
                    Slider(
                        value = trackLineOpacity,
                        onValueChange = {
                            trackLineOpacity = it
                        },
                        onValueChangeFinished = {
                            AppPrefs.sharedInstance.mapDrawLineAlpha = trackLineOpacity.toDouble()
                            onMapDrawLineAlphaChanged(trackLineOpacity.toDouble())
                        },
                        valueRange = 0f..1f,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFE53935),
                            thumbColor = Color(0xFFE53935),
                            inactiveTrackColor = Color(0xFF404040)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            )

            ConfigItem(
                label = "| 轨迹点透明度",
                content = {
                    Slider(
                        value = trackPointOpacity,
                        onValueChange = {
                            trackPointOpacity = it
                        },
                        onValueChangeFinished = {
                            AppPrefs.sharedInstance.mapDrawSpotAlpha = trackPointOpacity.toDouble()
                            onMapDrawSpotAlphaChanged(trackPointOpacity.toDouble())
                        },
                        valueRange = 0f..1f,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFE53935),
                            thumbColor = Color(0xFFE53935),
                            inactiveTrackColor = Color(0xFF404040)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            )

            ConfigItem(
                label = "| 轨迹线宽度",
                content = {
                    Slider(
                        value = trackLineWidth,
                        onValueChange = {
                            trackLineWidth = it
                        },
                        onValueChangeFinished = {
                            AppPrefs.sharedInstance.mapDrawLineWidth = trackLineWidth.toInt()
                            onMapDrawLineWidthChanged(trackLineWidth.toInt())
                        },
                        valueRange = 1f..10f,
                        steps = 9,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFE53935),
                            thumbColor = Color(0xFFE53935),
                            inactiveTrackColor = Color(0xFF404040)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            )


            ConfigItem(
                label = "| 轨迹连接距离",
                content = {
                    Slider(
                        value = trackConnectionDistance,
                        onValueChange = {
                            trackConnectionDistance = it
                        },
                        onValueChangeFinished = {
                            val newDistanceFilter = (trackConnectionDistance * 15000).toInt()
                            AppPrefs.sharedInstance.mapDrawLineDistanceFilter = newDistanceFilter
                            onMapDrawLineDistanceFilterChanged(newDistanceFilter)
                        },
                        valueRange = 0f..1f,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFE53935),
                            thumbColor = Color(0xFFE53935),
                            inactiveTrackColor = Color(0xFF404040)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            )
            Spacer(modifier = Modifier.height(12.dp))

            // GPS偏移过滤器配置
            var gpsOffsetFilterEnabled by remember { mutableStateOf(AppPrefs.sharedInstance.gpsOffsetFilterEnabled) }
            var gpsOffsetFilterSensitivity by remember { mutableStateOf(AppPrefs.sharedInstance.gpsOffsetFilterSensitivity.toFloat()) }

            ConfigItem(
                label = "| GPS偏移过滤",
                content = {
                    ConfigToggleButton(
                        text = "启用过滤",
                        isChecked = gpsOffsetFilterEnabled,
                        onCheckedChange = {
                            gpsOffsetFilterEnabled = it
                            AppPrefs.sharedInstance.gpsOffsetFilterEnabled = it
                        },
                        modifier = Modifier.width(94.dp)
                    )
                }
            )
            Spacer(modifier = Modifier.height(8.dp))

            if (gpsOffsetFilterEnabled) {
                ConfigItem(
                    label = "| 过滤敏感度",
                    content = {
                        Slider(
                            value = gpsOffsetFilterSensitivity,
                            onValueChange = {
                                gpsOffsetFilterSensitivity = it
                            },
                            onValueChangeFinished = {
                                AppPrefs.sharedInstance.gpsOffsetFilterSensitivity = gpsOffsetFilterSensitivity.toDouble()
                            },
                            valueRange = 0.5f..2.0f,
                            colors = SliderDefaults.colors(
                                activeTrackColor = Color(0xFFE53935),
                                thumbColor = Color(0xFFE53935),
                                inactiveTrackColor = Color(0xFF404040)
                            ),
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                )
                Spacer(modifier = Modifier.height(12.dp))
            }

            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}

@Composable
fun ConfigItem(label: String, date: String? = null, content: @Composable () -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            color = Color(0xFF555555),
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.width(88.dp) // 固定宽度确保对齐
        )
        if (date != null) {
            Text(
                text = date,
                color = Color.White.copy(alpha = 0.6f),
                style = MaterialTheme.typography.bodySmall,
//                modifier = Modifier.padding(end = 8.dp)
            )
        }
        Box(modifier = Modifier.weight(1f)) {
            content()
        }
    }
}

@Composable
fun ConfigToggleButton(
    text: String,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .height(28.dp)
            .border(
                width = 1.dp,
                color = if (isChecked) Color(0xFFE53935) else Color(0xFF666666),
                shape = RoundedCornerShape(14.dp)
            )
            .background(
                color = Color.Transparent,
                shape = RoundedCornerShape(14.dp)
            )
            .clickable { onCheckedChange(!isChecked) }
            .padding(horizontal = 8.dp, vertical = 2.dp)
        ,
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = if (isChecked) Color(0xFFE53935) else Color(0xFF999999),
            style = MaterialTheme.typography.labelSmall,
            fontSize = 10.sp
        )
    }
}

@Composable
fun MapStyleButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundImage: Painter? = null
) {
    // Map style button with background image and text overlay
    Box(
        modifier = modifier
            .size(width = 94.dp, height = 40.dp)
            .border(
                width = if (isSelected) 0.7.dp else 0.dp,
                color = if (isSelected) Color(0xFFE53935) else Color(0xFF666666),
                shape = RoundedCornerShape(12.dp)
            )
            .background(
                color = Color(0xFF1A1A1A),
                shape = RoundedCornerShape(12.dp)
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        // Background image
        if (backgroundImage != null) {
            Image(
                painter = backgroundImage,
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(12.dp)),
                contentScale = ContentScale.Crop
            )
        }

        // Semi-transparent overlay for better text readability
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    color = Color.Black.copy(alpha = 0.2f),
                    shape = RoundedCornerShape(12.dp)
                )
        )

        // Text overlay on the background
        Text(
            text = text,
            color = Color.White,
            style = MaterialTheme.typography.labelSmall,
            fontSize = 11.sp,
            modifier = Modifier
                .padding(start = 12.dp) // 添加左侧间距
                .align(alignment = Alignment.CenterStart) // 使文本靠左对齐
        )
    }
}

@Composable
fun ConfigSlider(label: String, value: Float, onValueChange: (Float) -> Unit, valueRange: ClosedFloatingPointRange<Float>, steps: Int) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = label,
            color = Color.White,
            style = MaterialTheme.typography.bodySmall,
            fontSize = 14.sp
        )
        Spacer(modifier = Modifier.height(8.dp))
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            steps = steps,
            colors = SliderDefaults.colors(
                activeTrackColor = Color(0xFFE53935), // Red to match design
                thumbColor = Color(0xFFE53935), // Red thumb
                inactiveTrackColor = Color(0xFF404040) // Lighter grey for inactive track
            ),
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
fun ConfigSwitchToggle(label: String, isChecked: Boolean, onCheckedChange: (Boolean) -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(text = label, color = Color.White, style = MaterialTheme.typography.bodySmall)
        Switch(checked = isChecked, onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color(0xFFB00020),
                checkedTrackColor = Color(0xFF2A2A2A),
                uncheckedThumbColor = Color.White,
                uncheckedTrackColor = Color(0xFF2A2A2A)
            )
        )
    }
}

@Composable
fun ColorSelectionItem(color: Color, isSelected: Boolean, onClick: () -> Unit, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .aspectRatio(1f) // Ensure perfect circle
            .background(color, CircleShape)
            .clickable(onClick = onClick)
            .border(
                width = if (isSelected) 2.dp else 0.dp,
                color = if (isSelected) Color.White else Color.Transparent,
                shape = CircleShape
            )
    )
}