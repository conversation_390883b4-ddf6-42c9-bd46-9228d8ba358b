package com.lfb.android.footprint.location

import android.content.Context
import android.location.Location
import android.os.Build
import android.util.Log
import com.lfb.android.footprint.Manager.RealmModelManager
import com.lfb.android.footprint.model.StepDataRealmModel
import com.lfb.android.footprint.prefs.AppPrefs
import java.io.File
import java.util.Calendar
import java.util.Date
import com.mapbox.geojson.Point
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class LocationDataRecorder(private val context: Context) {
    private val TAG = "LocationDataRecorder"
    public var lastLocation: Location? = null
    private var currentFile: File? = null
    private var currentRawFile: File? = null  // 原始数据文件

    companion object {
        @Volatile
        private var instance: LocationDataRecorder? = null

        fun getInstance(context: Context): LocationDataRecorder {
            return instance ?: synchronized(this) {
                instance ?: LocationDataRecorder(context.applicationContext).also { instance = it }
            }
        }
    }

    init {
        initializeLastLocation()
    }

    private fun initializeLastLocation() {
        // 获取最新的CSV文件
        val today = getTodayStartTimestamp(Date().time)
        val fileName = "${today}.csv"
        var locations = getLocationsByFileName(fileName)
        if (locations.count() > 0) {
            lastLocation = locations.last()
        }
    }

    private fun getLocationsByFileName(fileName: String): List<Location> {
        val locations = mutableListOf<Location>()
        val file = File(context.getExternalFilesDir(null), fileName)

        if (file.exists()) {
            try {
                val lines = file.readLines()
                for (line in lines.drop(1)) { // 跳过表头
                    val parts = line.split(",")
                    if (parts.size >= 10) {
                        val location = Location("").apply {
                            time = parts[0].toLong() * 1000 // 将秒转换为毫秒
                            latitude = parts[1].toDouble()
                            longitude = parts[2].toDouble()
                            accuracy = parts[3].toFloat()
                            speed = parts[4].toFloat()
                            bearing = parts[5].toFloat()
                            altitude = parts[7].toDouble()
                        }
                        locations.add(location)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error reading locations from file: ${e.message}")
            }
        } else {
            Log.d(TAG, "File not found: $fileName")
        }

        return locations
    }

    private fun getTodayStartTimestamp(timeInMillis: Long): Long {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timeInMillis
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis / 1000
    }

    private fun createNewFileIfNeeded(location: Location) {
        val lastLocationDay = lastLocation?.let { getTodayStartTimestamp(it.time) }
        val newLocationDay = getTodayStartTimestamp(location.time)

        // 如果是新的一天或者文件还未创建
        if (currentFile == null || (lastLocationDay != null && lastLocationDay != newLocationDay)) {
            val fileName = "${newLocationDay}.csv"
            currentFile = File(context.getExternalFilesDir(null), fileName)

            // 如果是新文件，写入表头
            if (!currentFile!!.exists()) {
                try {
                    currentFile!!.createNewFile()
                    currentFile!!.appendText(
                        "Timestamp,Latitude,Longitude,Accuracy,Speed,Bearing," +
                        "Distance,Altitude,HorizontalAccuracy,VerticalAccuracy\n"
                    )
                    Log.d(TAG, "Created new file: ${currentFile!!.absolutePath}")
                } catch (e: Exception) {
                    Log.e(TAG, "Error creating file: ${e.message}")
                }
            }
        }
    }

    private fun createNewRawFileIfNeeded(location: Location) {
        val lastLocationDay = lastLocation?.let { getTodayStartTimestamp(it.time) }
        val newLocationDay = getTodayStartTimestamp(location.time)

        // 如果是新的一天或者文件还未创建
        if (currentRawFile == null || (lastLocationDay != null && lastLocationDay != newLocationDay)) {
            val rawFileName = "${newLocationDay}_raw.csv"
            currentRawFile = File(context.getExternalFilesDir(null), rawFileName)

            // 如果是新文件，写入表头
            if (!currentRawFile!!.exists()) {
                try {
                    currentRawFile!!.createNewFile()
                    currentRawFile!!.appendText(
                        "Timestamp,Latitude,Longitude,Accuracy,Speed,Bearing," +
                        "Distance,Altitude,HorizontalAccuracy,VerticalAccuracy\n"
                    )
                    Log.d(TAG, "Created new raw file: ${currentRawFile!!.absolutePath}")
                } catch (e: Exception) {
                    Log.e(TAG, "Error creating raw file: ${e.message}")
                }
            }
        }
    }

    fun recordLocation(location: Location) {

        AppPrefs.sharedInstance.lastLocationlongitude = location.longitude
        AppPrefs.sharedInstance.lastLocationlatitude = location.latitude
        AppPrefs.sharedInstance.lastLocationTime = (location.time / 1000).toInt()

        try {
            createNewFileIfNeeded(location)

            val timestamp = location.time / 1000 // 转换为秒
            val distance = lastLocation?.distanceTo(location) ?: 0f

            val horizontalAccuracy = location.accuracy
            val verticalAccuracy = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                location.verticalAccuracyMeters
            } else {
                0f
            }

            val locationData = buildString {
                append("$timestamp,")
                append("${location.latitude},")
                append("${location.longitude},")
                append("${location.accuracy},")
                append("${location.speed},")
                append("${location.bearing},")
                append("$distance,")
                append("${location.altitude},")
                append("$horizontalAccuracy,")
                append("$verticalAccuracy\n")
            }

            currentFile?.appendText(locationData)
            Log.d(TAG, "Location recorded: $locationData")

            lastLocation = location
        } catch (e: Exception) {
            Log.e(TAG, "Error writing location data: ${e.message}")
        }
    }

    /**
     * 记录原始定位数据（未经过滤的）
     * 这个方法会被LocationManager在接收到原始GPS数据时调用
     */
    fun recordRawLocation(location: Location) {
        try {
            createNewRawFileIfNeeded(location)

            val timestamp = location.time / 1000 // 转换为秒
            val distance = lastLocation?.distanceTo(location) ?: 0f

            val horizontalAccuracy = location.accuracy
            val verticalAccuracy = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                location.verticalAccuracyMeters
            } else {
                0f
            }

            val locationData = buildString {
                append("$timestamp,")
                append("${location.latitude},")
                append("${location.longitude},")
                append("${location.accuracy},")
                append("${location.speed},")
                append("${location.bearing},")
                append("$distance,")
                append("${location.altitude},")
                append("$horizontalAccuracy,")
                append("$verticalAccuracy\n")
            }

            currentRawFile?.appendText(locationData)
            Log.d(TAG, "Raw location recorded: $locationData")

        } catch (e: Exception) {
            Log.e(TAG, "Error writing raw location data: ${e.message}")
        }
    }

    fun getCurrentFilePath(): String = currentFile?.absolutePath ?: ""

    fun getAllLocationFiles(): List<File> {
        val directory = context.getExternalFilesDir(null)
        return directory?.listFiles { file ->
            file.isFile && file.name.endsWith(".csv") && !file.name.endsWith("_raw.csv")
        }?.sortedBy { it.name } ?: emptyList()
    }

    fun getAllRawLocationFiles(): List<File> {
        val directory = context.getExternalFilesDir(null)
        return directory?.listFiles { file ->
            file.isFile && file.name.endsWith("_raw.csv")
        }?.sortedBy { it.name } ?: emptyList()
    }

    // 获取今日的轨迹点
    fun getTodayLocations(): List<Point> {
        val today = getTodayStartTimestamp(Date().time)
        val fileName = "${today}.csv"
        val locations = getLocationsByFileName(fileName)

        return locations.map { location ->
            Point.fromLngLat(location.longitude, location.latitude)
        }
    }

    fun getYesterdayLocations(): List<Point> {
        val today = getTodayStartTimestamp(Date().time)
        val yesterdayStart = today - 1 * 86400 // 转换为秒

        // 构建昨天的文件名
        val fileName = "$yesterdayStart.csv"
        val locations = getLocationsByFileName(fileName)

        return locations.map { location ->
            Point.fromLngLat(location.longitude, location.latitude)
        }
    }

    fun getLastSevenDaysLocations(): List<Point> {
        val today = getTodayStartTimestamp(Date().time)
        val startTime = today - 6 * 86400 // 转换为秒
        val lastSevenDaysLocations = mutableListOf<Point>()

        // 遍历过去7天的每一天
        for (i in 0..6) {
            val dayStart = startTime + 86400 * i // 每天的开始时间戳
            val fileName = "$dayStart.csv"
            val locations = getLocationsByFileName(fileName)
            locations.map { location ->
                lastSevenDaysLocations.add(Point.fromLngLat(location.longitude, location.latitude))
            }
        }

        return lastSevenDaysLocations
    }

    // 获取今日的原始轨迹点（未过滤的）
    fun getTodayRawLocations(): List<Point> {
        val today = getTodayStartTimestamp(Date().time)
        val fileName = "${today}_raw.csv"
        val locations = getLocationsByFileName(fileName)

        return locations.map { location ->
            Point.fromLngLat(location.longitude, location.latitude)
        }
    }

    fun getYesterdayRawLocations(): List<Point> {
        val today = getTodayStartTimestamp(Date().time)
        val yesterdayStart = today - 1 * 86400 // 转换为秒

        // 构建昨天的原始文件名
        val fileName = "${yesterdayStart}_raw.csv"
        val locations = getLocationsByFileName(fileName)

        return locations.map { location ->
            Point.fromLngLat(location.longitude, location.latitude)
        }
    }

    fun getLastSevenDaysRawLocations(): List<Point> {
        val today = getTodayStartTimestamp(Date().time)
        val startTime = today - 6 * 86400 // 转换为秒
        val lastSevenDaysRawLocations = mutableListOf<Point>()

        // 遍历过去7天的每一天
        for (i in 0..6) {
            val dayStart = startTime + 86400 * i // 每天的开始时间戳
            val fileName = "${dayStart}_raw.csv"
            val locations = getLocationsByFileName(fileName)
            locations.map { location ->
                lastSevenDaysRawLocations.add(Point.fromLngLat(location.longitude, location.latitude))
            }
        }

        return lastSevenDaysRawLocations
    }

    fun saveAllLocation() {
        val allDataFiles = getAllLocationFiles()
        CoroutineScope(Dispatchers.IO).launch {
            allDataFiles.forEach { file ->
                try {
                    val lines = file.readLines()
                    val batchSize = 1000
                    val locationBatch = mutableListOf<StepDataRealmModel>()

                    // 跳过表头，处理每一行数据
                    lines.drop(1).forEach { line ->
                        val parts = line.split(",")
                        if (parts.size >= 10) {
                            val timestamp = parts[0].toLong()  // 秒
                            val calendar = Calendar.getInstance().apply {
                                timeInMillis = timestamp * 1000
                            }

                            val stepData = StepDataRealmModel().apply {
                                this.dataTime = timestamp
                                this.latitude = parts[1].toDouble()
                                this.longitude = parts[2].toDouble()
                                this.year = calendar.get(Calendar.YEAR)
                                this.day = calendar.get(Calendar.DAY_OF_MONTH)
                                this.dayOfThisyear = calendar.get(Calendar.DAY_OF_YEAR)
                                this.speed = parts[4].toDoubleOrNull() ?: 0.0
                                this.heading = parts[5].toDoubleOrNull() ?: 0.0
                                this.distance = parts[6].toDoubleOrNull() ?: 0.0
                                this.altitude = parts[7].toDoubleOrNull() ?: 0.0
                                this.hAccuracy = parts[8].toDoubleOrNull() ?: 0.0
                                this.vAccuracy = parts[9].toDoubleOrNull() ?: 0.0
                            }
                            
                            locationBatch.add(stepData)

                            // 当达到批处理大小时，写入数据库
                            if (locationBatch.size >= batchSize) {
                                RealmModelManager.getInstance().writeBatchToRealm(locationBatch)
                                locationBatch.clear()
                            }
                        }
                    }

                    // 处理剩余的数据
                    if (locationBatch.isNotEmpty()) {
                        RealmModelManager.getInstance().writeBatchToRealm(locationBatch)
                    }

                    // 检查文件是否是当天的文件
                    val fileName = file.nameWithoutExtension
                    val fileDate = try {
                        fileName.toLong() * 1000 // 将文件名（秒时间戳）转换为毫秒
                    } catch (e: NumberFormatException) {
                        Log.e(TAG, "Invalid file name format: ${file.name}")
                        return@forEach
                    }

                    val today = Calendar.getInstance().apply {
                        set(Calendar.HOUR_OF_DAY, 0)
                        set(Calendar.MINUTE, 0)
                        set(Calendar.SECOND, 0)
                        set(Calendar.MILLISECOND, 0)
                    }.timeInMillis

                    val fileCalendar = Calendar.getInstance().apply {
                        timeInMillis = fileDate
                        set(Calendar.HOUR_OF_DAY, 0)
                        set(Calendar.MINUTE, 0)
                        set(Calendar.SECOND, 0)
                        set(Calendar.MILLISECOND, 0)
                    }.timeInMillis

                    // 如果不是当天的文件，删除它
                    if (fileCalendar != today) {
//                        if (file.delete()) {
//                            Log.d(TAG, "Deleted old file: ${file.name}")
//                        } else {
//                            Log.e(TAG, "Failed to delete file: ${file.name}")
//                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing file ${file.name}: ${e.message}")
                }
            }
        }
    }
}